package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
)

// DocumensoClient handles Documenso API operations
type DocumensoClient struct {
	APIKey  string
	BaseURL string
}

// NewDocumensoClient creates a new Documenso client
func NewDocumensoClient() *DocumensoClient {
	apiKey := os.Getenv("DOCUMENSO_KEY")
	if apiKey == "" {
		log.Printf("Warning: DOCUMENSO_KEY environment variable not set")
	}

	return &DocumensoClient{
		APIKey:  apiKey,
		BaseURL: "https://app.documenso.com/api/v1",
	}
}

// Recipient represents a document recipient
type Recipient struct {
	ID           int    `json:"id"` // For template recipients, this should be the template recipient ID
	Name         string `json:"name"`
	Email        string `json:"email"`
	Role         string `json:"role,omitempty"` // Usually "SIGNER"
	SigningOrder int    `json:"signingOrder"`   // Required by API
}

// CreateDocumentFromTemplateRequest represents the request payload
type CreateDocumentFromTemplateRequest struct {
	Title       string                 `json:"title,omitempty"`
	ExternalId  string                 `json:"externalId,omitempty"`
	Recipients  []Recipient            `json:"recipients"`
	Meta        map[string]interface{} `json:"meta,omitempty"`
	AuthOptions map[string]interface{} `json:"authOptions,omitempty"`
	FormValues  map[string]interface{} `json:"formValues,omitempty"`
}

// CreateDocumentFromTemplateResponse represents the API response
type CreateDocumentFromTemplateResponse struct {
	DocumentID int                 `json:"documentId"` // Documenso returns this as a number
	Title      string              `json:"title"`
	Status     string              `json:"status"`
	ExternalId string              `json:"externalId"`
	Recipients []RecipientResponse `json:"recipients,omitempty"`
	// Add other fields as needed based on actual API response
}

// RecipientResponse represents recipient data from API response
type RecipientResponse struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Email      string `json:"email"`
	Role       string `json:"role"`
	Token      string `json:"token,omitempty"`
	SigningUrl string `json:"signingUrl,omitempty"`
}

// CreateDocumentFromTemplate creates a new document from an existing template
func (client *DocumensoClient) CreateDocumentFromTemplate(templateID string, request CreateDocumentFromTemplateRequest) (*CreateDocumentFromTemplateResponse, error) {
	if client.APIKey == "" {
		return nil, fmt.Errorf("DOCUMENSO_KEY not configured")
	}

	// Build the API endpoint URL - correct template endpoint
	url := fmt.Sprintf("%s/templates/%s/generate-document", client.BaseURL, templateID)

	// Convert request to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	log.Printf("Creating document from template %s", templateID)
	log.Printf("Request payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", client.APIKey)

	// Send the request
	httpClient := &http.Client{}
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	log.Printf("Documenso API response status: %d", resp.StatusCode)
	log.Printf("Documenso API response body: %s", string(bodyBytes))

	// Check if request was successful
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("documenso API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	var response CreateDocumentFromTemplateResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	log.Printf("Document created successfully with ID: %d", response.DocumentID)
	return &response, nil
}

// ExtractTemplateIDFromURL extracts template ID from a Documenso template URL
// Example: https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq -> nr0_9M2wDXFwzi2m41WNq
func ExtractTemplateIDFromURL(templateURL string) string {
	// Simple extraction - assumes URL format: https://app.documenso.com/d/{templateID}
	if len(templateURL) > 0 {
		// Find the last slash and extract everything after it
		lastSlash := -1
		for i := len(templateURL) - 1; i >= 0; i-- {
			if templateURL[i] == '/' {
				lastSlash = i
				break
			}
		}
		if lastSlash != -1 && lastSlash < len(templateURL)-1 {
			return templateURL[lastSlash+1:]
		}
	}
	return templateURL // Return as-is if extraction fails
}

// CreateSurgassistsDocument is a convenience function to create a document from your specific template
// Template URL: https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq
func (client *DocumensoClient) CreateSurgassistsDocument(patientName, patientEmail, title string) (*CreateDocumentFromTemplateResponse, error) {
	return client.CreateSurgassistsDocumentWithExternalId(patientName, patientEmail, title, "")
}

// CreateSurgassistsDocumentWithExternalId is a convenience function to create a document from your specific template with external ID
// Template URL: https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq
func (client *DocumensoClient) CreateSurgassistsDocumentWithExternalId(patientName, patientEmail, title, externalId string) (*CreateDocumentFromTemplateResponse, error) {
	templateID := "nr0_9M2wDXFwzi2m41WNq" // Your template ID

	request := CreateDocumentFromTemplateRequest{
		Title:      title,
		ExternalId: externalId,
		Recipients: []Recipient{
			{
				ID:           0, // Try 0 - template recipient ID might be 0-indexed
				Name:         patientName,
				Email:        patientEmail,
				SigningOrder: 1, // Required by API
			},
		},
		Meta: map[string]interface{}{
			"subject":                fmt.Sprintf("Please sign: %s", title),
			"message":                "Please review and sign this document.",
			"signingOrder":           "PARALLEL",
			"distributionMethod":     "EMAIL",
			"typedSignatureEnabled":  true,
			"uploadSignatureEnabled": true,
			"drawSignatureEnabled":   true,
		},
		AuthOptions: map[string]interface{}{
			"globalAccessAuth": []interface{}{},
			"globalActionAuth": []interface{}{},
		},
		FormValues: map[string]interface{}{},
	}

	return client.CreateDocumentFromTemplate(templateID, request)
}
