package routes

import (
	"log"

	"github.com/applegold/surgassists-ar-backend/utils"
	"github.com/gofiber/fiber/v2"
)

// CreateDocumentFromTemplateRequest represents the incoming request
type CreateDocumentFromTemplateRequest struct {
	TemplateURL string `json:"template_url"`
	Title       string `json:"title,omitempty"`
	Recipients  []struct {
		Name  string `json:"name"`
		Email string `json:"email"`
		Role  string `json:"role,omitempty"` // Defaults to "SIGNER"
	} `json:"recipients,omitempty"`
	Meta map[string]interface{} `json:"meta,omitempty"`
}

// CreateDocumentFromTemplate creates a new document from a Documenso template
func CreateDocumentFromTemplate(c *fiber.Ctx) error {
	log.Printf("=== CREATE DOCUMENT FROM TEMPLATE ===")

	// Parse request body
	var request CreateDocumentFromTemplateRequest
	if err := c.<PERSON>er(&request); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate required fields
	if request.TemplateURL == "" {
		log.Printf("Missing template URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Template URL is required",
		})
	}

	log.Printf("Creating document from template: %s", request.TemplateURL)

	// Extract template ID from URL
	templateID := utils.ExtractTemplateIDFromURL(request.TemplateURL)
	if templateID == "" {
		log.Printf("Failed to extract template ID from URL: %s", request.TemplateURL)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid template URL format",
		})
	}

	log.Printf("Extracted template ID: %s", templateID)

	// Create Documenso client
	client := utils.NewDocumensoClient()

	// Convert recipients
	var recipients []utils.Recipient
	for _, r := range request.Recipients {
		role := r.Role
		if role == "" {
			role = "SIGNER" // Default role
		}
		recipients = append(recipients, utils.Recipient{
			Name:  r.Name,
			Email: r.Email,
			Role:  role,
		})
	}

	// Prepare the request
	docRequest := utils.CreateDocumentFromTemplateRequest{
		Title:      request.Title,
		Recipients: recipients,
		Meta:       request.Meta,
	}

	// Create document from template
	response, err := client.CreateDocumentFromTemplate(templateID, docRequest)
	if err != nil {
		log.Printf("Failed to create document from template: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to create document from template",
			"details": err.Error(),
		})
	}

	log.Printf("Document created successfully with ID: %d", response.DocumentID)

	return c.JSON(fiber.Map{
		"message":     "Document created from template successfully",
		"template_id": templateID,
		"document_id": response.DocumentID,
		"title":       response.Title,
		"externalId":  response.ExternalId,
		"status":      response.Status,
		"response":    response,
	})
}
