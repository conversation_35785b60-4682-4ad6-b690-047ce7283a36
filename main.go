package main

import (
	"crypto/sha256"
	"crypto/subtle"
	"log"
	"os"
	"strings"

	"github.com/applegold/surgassists-ar-backend/routes"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/keyauth"
	"github.com/joho/godotenv"
)

func setUpRoutes(app *fiber.App, apiKey string) {

	authMiddleware := keyauth.New(keyauth.Config{
		Validator: func(c *fiber.Ctx, key string) (bool, error) {
			hashedAPIKey := sha256.Sum256([]byte(apiKey))
			hashedKey := sha256.Sum256([]byte(key))

			if subtle.ConstantTimeCompare(hashedAPIKey[:], hashedKey[:]) == 1 {
				return true, nil
			}
			return false, keyauth.ErrMissingOrMalformedAPIKey
		},
	})

	app.Get("/", routes.Hello)

	app.Get("/allowed", authMiddleware, routes.Allowed)

	app.Post("/start-patient-consent", authMiddleware, routes.StartPatientConsent)

	app.Post("/log-stats", authMiddleware, routes.LogStats)

	app.Get("/patient-consent-viewed/:id", authMiddleware, routes.PatientConsentViewed)

	app.Get("/make-qr-code/:id", authMiddleware, routes.MakeQRCode)

	app.Get("/unity-ar-status/:id", authMiddleware, routes.UnityARStatus)

	app.Get("/patient-started-signature/:id", authMiddleware, routes.PatientStartedSignature)

	app.Post("/doc-done", routes.DocumensoDone)

	app.Get("/procedures", authMiddleware, routes.GetProcedures)

	app.Get("/get-documents-upload-list/:id", authMiddleware, routes.GetDocumentsUploadList)

	app.Delete("/delete-document/:id", authMiddleware, routes.DeleteDoc)

	//old version
	app.Post("/upload-document", authMiddleware, routes.UploadDocument)
	//new version
	app.Post("/upload-document-processing", authMiddleware, routes.UploadDocumentProcessing)

	app.Get("/reports", authMiddleware, routes.Reports)

	app.Get("/get-pdf-documents-for-signature/:id", authMiddleware, routes.GetPDFdocumentsForSignature)

	app.Post("/create-document-from-template", authMiddleware, routes.CreateDocumentFromTemplate)

	app.Post("/create-surgassists-document", authMiddleware, routes.CreateSurgassistsDocument)

	//admin routes
	app.Get("/admin-get-list-users", authMiddleware, routes.AdminGetListUsers)

	app.Post("/admin-create-user", authMiddleware, routes.AdminCreateUser)

	app.Get("/admin-get-user-info/:id", authMiddleware, routes.AdminGetUserInfo)

	app.Delete("/admin-delete-user/:id", authMiddleware, routes.DeleteUser)

}

func main() {
	// Load environment variables from .env file
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	// Get API key from environment variable
	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		log.Fatal("API_KEY environment variable is required")
	}

	app := fiber.New()

	// Configure CORS
	// Allow only your frontend domain
	allowedOrigins := []string{
		"https://surgassists-connect.online",
		"https://www.surgassists-connect.online", // Include www variant if needed
	}

	// For development (remove in production)
	if os.Getenv("ENV") == "development" {
		allowedOrigins = append(allowedOrigins, "http://localhost:3000")
	}

	app.Use(cors.New(cors.Config{
		AllowOrigins:     strings.Join(allowedOrigins, ","),
		AllowMethods:     "GET,POST,HEAD,PUT,DELETE,PATCH,OPTIONS",
		AllowHeaders:     "Origin,Content-Type,Accept,Authorization,X-Requested-With",
		AllowCredentials: true,
	}))

	setUpRoutes(app, apiKey)

	log.Fatal(app.Listen("0.0.0.0:6008"))
}
