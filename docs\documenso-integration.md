# Documenso Integration - Clean Reusable Functions

This document explains the new clean, reusable Documenso integration functions.

## Overview

We've created reusable functions for Documenso operations to avoid code duplication and maintain cleaner code.

## Files Created

1. **`utils/documenso.go`** - Core Documenso client and utilities
2. **`routes/create_document_from_template.go`** - Generic template document creation
3. **`routes/create_surgassists_document.go`** - Specific function for your template

## Template Information

- **Template URL**: `https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq`
- **Template ID**: `nr0_9M2wDXFwzi2m41WNq`

## API Endpoints

### 1. Generic Template Document Creation

**Endpoint**: `POST /create-document-from-template`

**Request Body**:
```json
{
  "template_url": "https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq",
  "title": "Custom Document Title",
  "recipients": [
    {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "SIGNER"
    }
  ],
  "meta": {
    "custom_field": "value"
  }
}
```

**Response**:
```json
{
  "message": "Document created from template successfully",
  "template_id": "nr0_9M2wDXFwzi2m41WNq",
  "document_id": "generated_document_id",
  "title": "Custom Document Title",
  "status": "DRAFT",
  "response": { /* full API response */ }
}
```

### 2. Surgassists Specific Document Creation

**Endpoint**: `POST /create-surgassists-document`

**Request Body**:
```json
{
  "patient_name": "John Doe",
  "patient_email": "<EMAIL>",
  "title": "Surgassists Document"
}
```

**Response**:
```json
{
  "message": "Surgassists document created successfully",
  "document_id": "generated_document_id",
  "title": "Surgassists Document",
  "status": "DRAFT",
  "patient_name": "John Doe",
  "patient_email": "<EMAIL>",
  "response": { /* full API response */ }
}
```

## Usage in Code

### Using the Documenso Client Directly

```go
import "github.com/applegold/surgassists-ar-backend/utils"

// Create client
client := utils.NewDocumensoClient()

// Create document from any template
response, err := client.CreateDocumentFromTemplate("template_id", utils.CreateDocumentFromTemplateRequest{
    Title: "My Document",
    Recipients: []utils.Recipient{
        {
            Name:  "John Doe",
            Email: "<EMAIL>",
            Role:  "SIGNER",
        },
    },
})

// Or use the convenience function for your specific template
response, err := client.CreateSurgassistsDocument("John Doe", "<EMAIL>", "Document Title")
```

## Environment Variables Required

- `DOCUMENSO_KEY` - Your Documenso API key

## Benefits of This Approach

1. **Reusable**: Functions can be used across different parts of your application
2. **Clean**: Separated concerns - utilities in `utils/`, routes in `routes/`
3. **Maintainable**: Changes to Documenso integration only need to be made in one place
4. **Flexible**: Generic function for any template, specific function for your template
5. **Type Safe**: Proper Go structs for requests and responses

## Next Steps

You can now use these functions in your existing code instead of duplicating Documenso API calls. The old code remains unchanged as requested.
