package routes

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/skip2/go-qrcode"
)

// PatientConsentRequest represents the incoming JSON payload
type PatientConsentRequest struct {
	ID        string `json:"$id"`
	UserID    string `json:"UserID"`
	Email     string `json:"Email"`
	Status    string `json:"Status"`
	CreatedAt string `json:"$createdAt"`
}

// PostalEmailRequest represents the request structure for Postal API
type PostalEmailRequest struct {
	To        []string `json:"to"`
	From      string   `json:"from"`
	Subject   string   `json:"subject"`
	PlainBody string   `json:"plain_body"`
}

// AppwriteUpdateRequest represents the request structure for updating Appwrite document
type AppwriteUpdateRequest struct {
	Data map[string]interface{} `json:"data"`
}

// getAppwriteDocument retrieves a document from Appwrite database
func getAppwriteDocument(documentID string) (map[string]interface{}, error) {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return nil, fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE GET REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: GET")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	var responseBody map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&responseBody); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %v", responseBody)
		return nil, fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document retrieved successfully for ID: %s", documentID)
	return responseBody, nil
}

// updateAppwriteDocumentStatus updates the document status in Appwrite database with a custom status
func updateAppwriteDocumentStatus(documentID, status string) error {
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	consentCollection := os.Getenv("CONSENT_COLLECTION")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteKey == "" || appwriteEndpoint == "" || consentCollection == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Use default values if not specified
	if appwriteProjectID == "" {
		appwriteProjectID = "default"
	}
	if appwriteDatabaseID == "" {
		appwriteDatabaseID = "default"
	}

	// Create the update request
	updateRequest := AppwriteUpdateRequest{
		Data: map[string]interface{}{
			"Status": status,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal appwrite update request: %v", err)
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Log the request details for debugging
	log.Printf("=== APPWRITE UPDATE REQUEST ===")
	log.Printf("Full URL: %s", url)
	log.Printf("Method: PATCH")
	log.Printf("Headers:")
	log.Printf("  Content-Type: application/json")
	log.Printf("  X-Appwrite-Project: %s", appwriteProjectID)
	log.Printf("  X-Appwrite-Key: %s", appwriteKey)
	log.Printf("Request Body: %s", string(jsonData))
	log.Printf("===============================")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update appwrite document: %v", err)
	}
	defer resp.Body.Close()

	// Read response body for better error debugging
	body, _ := json.Marshal(resp.Body)
	log.Printf("Appwrite response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite error response: %s", string(body))
		return fmt.Errorf("appwrite service returned status: %d", resp.StatusCode)
	}

	log.Printf("Appwrite document %s updated successfully with status '%s'", documentID, status)
	return nil
}

// updateAppwriteDocument updates the document status in Appwrite database to "Email Sent"
func updateAppwriteDocument(documentID string) error {
	return updateAppwriteDocumentStatus(documentID, "Email Sent")
}

// sendEmail sends an email using the Postal mail service
func sendEmail(toEmail, subject, body string) error {
	mailServerAPIKey := os.Getenv("MAIL_SERVER_API_KEY")
	mailServiceURL := os.Getenv("MAIL_SERVICE_URL")

	if mailServerAPIKey == "" || mailServiceURL == "" {
		return fmt.Errorf("mail server configuration missing")
	}

	emailRequest := PostalEmailRequest{
		To:        []string{toEmail},
		From:      "<EMAIL>", // You may want to make this configurable
		Subject:   subject,
		PlainBody: body,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(emailRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", mailServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Server-API-Key", mailServerAPIKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("email service returned status: %d", resp.StatusCode)
	}

	log.Printf("Email sent successfully to %s", toEmail)
	return nil
}

func Hello(c *fiber.Ctx) error {

	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func StartPatientConsent(c *fiber.Ctx) error {
	var request PatientConsentRequest

	// Parse the JSON body
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received patient consent request:")
	log.Printf("  ID: %s", request.ID)
	log.Printf("  UserID: %s", request.UserID)
	log.Printf("  Email: %s", request.Email)
	log.Printf("  Status: %s", request.Status)
	log.Printf("  CreatedAt: %s", request.CreatedAt)

	// Send email to the patient
	patientPortalBaseURL := os.Getenv("PATIENT_PORTAL_BASE_URL")
	if patientPortalBaseURL == "" {
		patientPortalBaseURL = "https://surgassists-connect.online"
	}

	// Original consent email content
	subject := "Medical AR App - Content Request"
	body := fmt.Sprintf("We are reaching out via email to request content for a medical AR app.\n\nPlease visit: %s/patient-consent-first/%s", patientPortalBaseURL, request.ID)

	if err := sendEmail(request.Email, subject, body); err != nil {
		log.Printf("Failed to send email to %s: %v", request.Email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to send email",
			"message": "Patient consent request received but email sending failed",
			"data":    request,
		})
	}

	log.Printf("Email sent successfully to %s for patient ID: %s", request.Email, request.ID)

	// Update Appwrite document status to "Email Sent"
	if err := updateAppwriteDocument(request.ID); err != nil {
		log.Printf("Failed to update Appwrite document %s: %v", request.ID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update database",
			"message":    "Email sent successfully but database update failed",
			"data":       request,
			"email_sent": true,
			"db_updated": false,
		})
	}

	log.Printf("Appwrite document updated successfully for patient ID: %s", request.ID)

	return c.JSON(fiber.Map{
		"message":    "Patient consent request received, logged, email sent, and database updated successfully",
		"data":       request,
		"email_sent": true,
		"db_updated": true,
	})
}

func LogStats(c *fiber.Ctx) error {
	// Parse the JSON body
	var statsData map[string]interface{}
	if err := c.BodyParser(&statsData); err != nil {
		log.Printf("Error parsing stats request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Extract app and qrcode from the request
	app, appOk := statsData["app"].(string)
	qrcode, qrcodeOk := statsData["qrcode"].(string)

	if !appOk || !qrcodeOk || app == "" || qrcode == "" {
		log.Printf("Missing or invalid required fields in stats")
		return c.Status(400).JSON(fiber.Map{
			"error": "App and qrcode fields are required",
		})
	}

	// Log the received stats data
	log.Printf("=== STATS RECEIVED ===")
	log.Printf("App: %s", app)
	log.Printf("QR Code: %s", qrcode)
	log.Printf("Timestamp: %s", c.Get("Date"))
	log.Printf("User Agent: %s", c.Get("User-Agent"))
	log.Printf("IP Address: %s", c.IP())
	log.Printf("=== END STATS ===")

	// Save to Appwrite Reports collection
	if err := saveStatsToAppwrite(app, qrcode); err != nil {
		log.Printf("Failed to save stats to Appwrite: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to save stats to database",
		})
	}

	log.Printf("Stats saved successfully to Reports collection")

	// Return success response
	return c.JSON(fiber.Map{
		"message": "Stats logged and saved successfully",
		"app":     app,
		"qrcode":  qrcode,
	})
}

func PatientConsentViewed(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Patient consent viewed for ID: %s", patientID)

	// Get the document from Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to retrieve patient information",
			"patient_id": patientID,
		})
	}

	// Update Appwrite document status to "Patient Open Form"
	if err := updateAppwriteDocumentStatus(patientID, "Patient Open Form"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		// Don't return error here, just log it - we still want to return the document
		return c.JSON(fiber.Map{
			"message":        "Patient consent viewed successfully",
			"patient_id":     patientID,
			"document":       document,
			"status_updated": false,
		})
	} else {
		log.Printf("Document status updated to 'Patient Open Form' for ID: %s", patientID)
	}

	return c.JSON(fiber.Map{
		"message":        "Patient consent viewed successfully",
		"patient_id":     patientID,
		"document":       document,
		"status_updated": true,
	})
}

func MakeQRCode(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for QR code generation")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking document exists for patient ID: %s", patientID)

	// Check if the document exists in Appwrite before generating QR code
	_, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	log.Printf("Document found, updating status to 'QR Generated' for patient ID: %s", patientID)

	// Update Appwrite document status to "QR Generated"
	if err := updateAppwriteDocumentStatus(patientID, "QR Generated"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update document status",
			"patient_id": patientID,
		})
	}

	log.Printf("Status updated, generating QR code for patient ID: %s", patientID)

	// Generate QR code containing just the patient ID (for AR app scanning)
	qrCodeBytes, err := qrcode.Encode(patientID, qrcode.Medium, 256)
	if err != nil {
		log.Printf("Failed to generate QR code for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to generate QR code",
		})
	}

	// Convert to base64 for JSON response (useful for email embedding)
	qrCodeBase64 := base64.StdEncoding.EncodeToString(qrCodeBytes)

	log.Printf("QR code generated successfully for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":         "QR code generated successfully",
		"patient_id":      patientID,
		"qr_code_content": patientID,
		"qr_code_base64":  qrCodeBase64,
		"qr_code_size":    len(qrCodeBytes),
	})
}

func UnityARStatus(c *fiber.Ctx) error {

	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for Unity AR status update")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking Unity AR status for patient ID: %s", patientID)

	// Check if the document exists in Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	log.Printf("Document found for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":    "Unity AR status updated successfully",
		"patient_id": patientID,
		"document":   document,
	})

}

func PatientStartedSignature(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL for patient started signature")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Checking patient started signature for ID: %s", patientID)

	// Check if the document exists in Appwrite
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to retrieve document for ID %s: %v", patientID, err)
		return c.Status(404).JSON(fiber.Map{
			"error":      "Patient document not found",
			"patient_id": patientID,
		})
	}

	// Extract email from the document
	var email string
	if emailValue, exists := document["Email"]; exists {
		if emailStr, ok := emailValue.(string); ok {
			email = emailStr
		}
	}

	// Start Document sign using documenso
	// Send email to the patient with document signing link
	docToSignURL := os.Getenv("DOC_TO_SIGN")
	if docToSignURL == "" {
		log.Printf("DOC_TO_SIGN environment variable not set")
		return c.Status(500).JSON(fiber.Map{
			"error": "Document signing URL not configured",
		})
	}

	// Create document signing email content
	subject := "You have a document to sign"
	body := fmt.Sprintf("Dear Patient,\n\nYou have a document that requires your signature.\n\nPlease click the link below to sign your document:\n%s\n\nThank you,\nSurgAssists Team", docToSignURL)

	// Send the email
	if err := sendEmail(email, subject, body); err != nil {
		log.Printf("Failed to send document signing email to %s: %v", email, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to send document signing email",
			"patient_id": patientID,
		})
	}

	log.Printf("Document signing email sent successfully to %s", email)

	log.Printf("Document found for patient ID: %s, email: %s", patientID, email)

	// Update Appwrite document status to "Patient Started Signature"
	if err := updateAppwriteDocumentStatus(patientID, "Patient Started Signature"); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		return c.Status(500).JSON(fiber.Map{
			"error":      "Failed to update document status",
			"patient_id": patientID,
		})
	}

	log.Printf("Document status updated to 'Patient Started Signature' for patient ID: %s", patientID)

	return c.JSON(fiber.Map{
		"message":    "Patient started signature successfully",
		"patient_id": patientID,
		"document":   document,
	})
}

func DocumensoDone(c *fiber.Ctx) error {
	// TODO: Implement Direct Template Embedding instead of Documenso API
	// Commenting out Documenso webhook code for now - will implement direct template embedding later

	log.Printf("=== DOCUMENSO WEBHOOK RECEIVED (TEMPORARILY DISABLED) ===")

	/*
	// Get the ID from the URL parameter

	log.Printf("=== DOCUMENSO WEBHOOK RECEIVED ===")

	// Validate webhook secret
	webDocHook := os.Getenv("WEB_DOC_HOOK")
	if webDocHook == "" {
		log.Printf("WEB_DOC_HOOK environment variable not set")
		return c.Status(500).JSON(fiber.Map{
			"error": "Webhook configuration missing",
		})
	}

	documensoSecret := c.Get("X-Documenso-Secret")
	if documensoSecret != webDocHook {
		log.Printf("Invalid webhook secret. Expected: %s, Got: %s", webDocHook, documensoSecret)
		return c.Status(401).JSON(fiber.Map{
			"error": "No Access",
		})
	}

	log.Printf("Webhook secret validated successfully")

	// Log all headers
	log.Printf("Headers received from Documenso:")
	for key, values := range c.GetReqHeaders() {
		for _, value := range values {
			log.Printf("  %s: %s", key, value)
		}
	}
	*/

	// Log request method and URL
	log.Printf("Method: %s", c.Method())
	log.Printf("URL: %s", c.OriginalURL())
	log.Printf("Remote IP: %s", c.IP())

	// Log body if present
	body := c.Body()
	if len(body) > 0 {
		log.Printf("Body: %s", string(body))
	} else {
		log.Printf("Body: (empty)")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.JSON(fiber.Map{
			"message": "Documenso webhook received but no body",
		})
	}

	// Parse the JSON body
	var webhookData map[string]interface{}
	if err := json.Unmarshal(body, &webhookData); err != nil {
		log.Printf("Failed to parse webhook JSON: %v", err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Check if this is a DOCUMENT_COMPLETED event
	event, exists := webhookData["event"]
	if !exists || event != "DOCUMENT_COMPLETED" {
		log.Printf("Event is not DOCUMENT_COMPLETED, ignoring webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.JSON(fiber.Map{
			"message": "Event ignored - not DOCUMENT_COMPLETED",
		})
	}

	log.Printf("Processing DOCUMENT_COMPLETED event")

	// Extract payload
	payload, exists := webhookData["payload"]
	if !exists {
		log.Printf("No payload found in webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "No payload in webhook",
		})
	}

	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		log.Printf("Payload is not a valid object")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid payload format",
		})
	}

	// Extract email from recipients
	var email string
	var documentDataId string
	var status string

	// Get documentDataId and status
	if docDataId, exists := payloadMap["documentDataId"]; exists {
		if docDataIdStr, ok := docDataId.(string); ok {
			documentDataId = docDataIdStr
		}
	}

	if statusVal, exists := payloadMap["status"]; exists {
		if statusStr, ok := statusVal.(string); ok {
			status = statusStr
		}
	}

	// Get email from recipients array
	if recipients, exists := payloadMap["recipients"]; exists {
		if recipientsArray, ok := recipients.([]interface{}); ok && len(recipientsArray) > 0 {
			if recipient, ok := recipientsArray[0].(map[string]interface{}); ok {
				if emailVal, exists := recipient["email"]; exists {
					if emailStr, ok := emailVal.(string); ok {
						email = emailStr
					}
				}
			}
		}
	}

	log.Printf("Extracted data - Email: %s, DocumentDataId: %s, Status: %s", email, documentDataId, status)

	if email == "" || documentDataId == "" {
		log.Printf("Missing required data from webhook")
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(400).JSON(fiber.Map{
			"error": "Missing email or documentDataId in webhook",
		})
	}

	// Look up document ID from Appwrite using email
	log.Printf("Looking up Appwrite document by email: %s", email)
	patientID, err := getAppwriteDocumentByEmail(email)
	if err != nil {
		log.Printf("Failed to find document by email %s: %v", email, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(404).JSON(fiber.Map{
			"error": "Patient document not found by email",
		})
	}

	log.Printf("Found patient ID: %s for email: %s", patientID, email)

	// Check if document is already COMPLETED to prevent duplicate processing
	log.Printf("Checking current status of document: %s", patientID)
	document, err := getAppwriteDocument(patientID)
	if err != nil {
		log.Printf("Failed to get document status for ID %s: %v", patientID, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to check document status",
		})
	}

	// Check current status
	if currentStatus, exists := document["Status"]; exists {
		if statusStr, ok := currentStatus.(string); ok {
			log.Printf("Current document status: %s", statusStr)
			if statusStr == "COMPLETED" {
				log.Printf("Document already COMPLETED, skipping duplicate processing")
				log.Printf("=== END DOCUMENSO WEBHOOK ===")
				return c.JSON(fiber.Map{
					"message":    "Document already completed - no action taken",
					"patient_id": patientID,
					"status":     "COMPLETED",
				})
			}
		}
	}

	log.Printf("Document status is not COMPLETED, proceeding with processing")

	// Send QR code email
	patientPortalBaseURL := os.Getenv("PATIENT_PORTAL_BASE_URL")
	if patientPortalBaseURL == "" {
		patientPortalBaseURL = "https://surgassists-connect.online"
	}

	qrCodeURL := fmt.Sprintf("%s/qr-display/%s", patientPortalBaseURL, patientID)
	subject := "Here is your QR code to start the AR app"
	emailBody := fmt.Sprintf(`Dear Patient,

Congratulations! Your document has been successfully signed and completed.

You can now access your personalized AR experience by using the QR code generator below:

%s

Simply visit the link above to generate your unique QR code, then scan it with the AR app to begin your immersive medical experience.

Thank you for completing the signing process.

Best regards,
SurgAssists Team`, qrCodeURL)

	log.Printf("Sending QR code email to: %s", email)
	if err := sendEmail(email, subject, emailBody); err != nil {
		log.Printf("Failed to send QR code email to %s: %v", email, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to send QR code email",
		})
	}

	log.Printf("QR code email sent successfully to: %s", email)

	// Update Appwrite document with COMPLETED status and DocumentDataID
	log.Printf("Updating Appwrite document status to COMPLETED and saving DocumentDataID")
	if err := updateAppwriteDocumentWithDataID(patientID, "COMPLETED", documentDataId); err != nil {
		log.Printf("Failed to update document status for ID %s: %v", patientID, err)
		log.Printf("=== END DOCUMENSO WEBHOOK ===")
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to update document status",
		})
	}

	log.Printf("Document updated successfully - Status: COMPLETED, DocumentDataID: %s", documentDataId)
	log.Printf("=== END DOCUMENSO WEBHOOK ===")

	return c.JSON(fiber.Map{
		"message":          "Documenso document completed successfully",
		"patient_id":       patientID,
		"email":            email,
		"document_data_id": documentDataId,
		"status":           "COMPLETED",
	})

}

// getAppwriteDocumentByEmail searches for a document in Appwrite by email address
func getAppwriteDocumentByEmail(email string) (string, error) {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")
	consentCollection := os.Getenv("CONSENT_COLLECTION")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" || consentCollection == "" {
		return "", fmt.Errorf("appwrite configuration missing")
	}

	// Build the URL with query to search by email
	// Try different query formats for Appwrite
	requestURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, consentCollection)

	log.Printf("Appwrite search URL: %s", requestURL)
	log.Printf("Searching for email: %s", email)

	// Create HTTP request
	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send appwrite request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read the response body for error details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite error response: %s", string(bodyBytes))
		return "", fmt.Errorf("appwrite returned status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("failed to decode appwrite response: %v", err)
	}

	// Check if documents were found
	documents, exists := result["documents"]
	if !exists {
		return "", fmt.Errorf("no documents field in response")
	}

	documentsArray, ok := documents.([]interface{})
	if !ok || len(documentsArray) == 0 {
		return "", fmt.Errorf("no documents found in collection")
	}

	log.Printf("Found %d documents in collection, searching for email: %s", len(documentsArray), email)

	// Search through all documents for matching email
	for i, doc := range documentsArray {
		docMap, ok := doc.(map[string]interface{})
		if !ok {
			log.Printf("Document %d has invalid format", i)
			continue
		}

		// Check if this document has the matching email
		if emailField, exists := docMap["Email"]; exists {
			if emailStr, ok := emailField.(string); ok {
				log.Printf("Document %d has email: %s", i, emailStr)
				if emailStr == email {
					// Found matching document, get its ID
					if docID, exists := docMap["$id"]; exists {
						if docIDStr, ok := docID.(string); ok {
							log.Printf("Found matching document with ID: %s", docIDStr)
							return docIDStr, nil
						}
					}
				}
			}
		}
	}

	return "", fmt.Errorf("no document found with email: %s", email)
}

// updateAppwriteDocumentWithDataID updates both status and DocumentDataID in Appwrite
func updateAppwriteDocumentWithDataID(documentID, status, documentDataID string) error {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")
	consentCollection := os.Getenv("CONSENT_COLLECTION")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" || consentCollection == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Build the URL
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, documentID)

	log.Printf("Appwrite update URL: %s", url)

	// Create the update payload - Appwrite requires data to be wrapped in "data" object
	updateData := map[string]interface{}{
		"data": map[string]interface{}{
			"Status":         status,
			"DocumentDataID": documentDataID,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("failed to marshal update data: %v", err)
	}

	log.Printf("Update payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send appwrite request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read the response body for error details
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite update error response: %s", string(bodyBytes))
		return fmt.Errorf("appwrite returned status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	log.Printf("Successfully updated document %s with status: %s and DocumentDataID: %s", documentID, status, documentDataID)
	return nil
}

// saveStatsToAppwrite saves stats data to the Reports collection
func saveStatsToAppwrite(appName, qrCode string) error {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		return fmt.Errorf("appwrite configuration missing")
	}

	// Reports collection ID
	reportsCollection := "68540de400285bfcb5b6"

	// Build the URL for creating a document in Reports collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, reportsCollection)

	// Create the document payload
	documentData := map[string]interface{}{
		"documentId": "unique()", // Let Appwrite generate unique ID
		"data": map[string]interface{}{
			"AppName": appName,
			"QRCode":  qrCode,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(documentData)
	if err != nil {
		return fmt.Errorf("failed to marshal stats data: %v", err)
	}

	log.Printf("Saving stats to Reports collection: %s", url)
	log.Printf("Stats payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create appwrite stats request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send appwrite stats request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body for error details
	bodyBytes, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusCreated {
		log.Printf("Appwrite stats creation error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return fmt.Errorf("appwrite returned status: %d, response: %s", resp.StatusCode, string(bodyBytes))
	}

	log.Printf("Stats document created successfully in Reports collection")
	return nil
}

func AdminGetListUsers(c *fiber.Ctx) error {
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" {
		log.Printf("Appwrite configuration missing for admin users list")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Build the Appwrite Users API URL
	url := fmt.Sprintf("%s/users", appwriteEndpoint)

	log.Printf("Fetching users from Appwrite: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite users request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite users request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch users",
		})
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		log.Printf("Appwrite users API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Appwrite API error",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var usersResponse map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&usersResponse); err != nil {
		log.Printf("Failed to decode Appwrite users response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse users response",
		})
	}

	// Log success
	if users, exists := usersResponse["users"]; exists {
		if usersArray, ok := users.([]interface{}); ok {
			log.Printf("Successfully fetched %d users from Appwrite", len(usersArray))
		}
	}

	// Return the users data
	return c.JSON(fiber.Map{
		"message": "Users fetched successfully",
		"data":    usersResponse,
	})
}

func AdminCreateUser(c *fiber.Ctx) error {
	// Parse the JSON body
	var userData map[string]interface{}
	if err := c.BodyParser(&userData); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid JSON payload",
		})
	}

	// Log the received data
	log.Printf("Received user data:")
	log.Printf("  Email: %s", userData["email"])
	log.Printf("  Password: %s", userData["password"])
	log.Printf("  Name: %s", userData["name"])

	// Validate required fields
	email, emailOk := userData["email"].(string)
	password, passwordOk := userData["password"].(string)
	name, nameOk := userData["name"].(string)

	if !emailOk || !passwordOk || !nameOk || email == "" || password == "" || name == "" {
		log.Printf("Missing or invalid required fields")
		return c.Status(400).JSON(fiber.Map{
			"error": "Email, password, and name are required",
		})
	}

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" {
		log.Printf("Appwrite configuration missing for user creation")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Build the Appwrite Users API URL
	url := fmt.Sprintf("%s/users", appwriteEndpoint)

	// Create user payload for Appwrite
	userPayload := map[string]interface{}{
		"userId":   "unique()", // Let Appwrite generate unique ID
		"email":    email,
		"password": password,
		"name":     name,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(userPayload)
	if err != nil {
		log.Printf("Failed to marshal user data: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare user data",
		})
	}

	log.Printf("Creating user in Appwrite: %s", url)
	log.Printf("User payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Failed to create Appwrite user request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite user creation request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusCreated {
		log.Printf("Appwrite user creation error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to create user in Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the successful response
	var createdUser map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &createdUser); err != nil {
		log.Printf("Failed to parse created user response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse user creation response",
		})
	}

	log.Printf("User created successfully in Appwrite with ID: %s", createdUser["$id"])

	// Return success response
	return c.Status(201).JSON(fiber.Map{
		"message": "User created successfully",
		"user":    createdUser,
	})
}

func AdminGetUserInfo(c *fiber.Ctx) error {
	// Get the user ID from the URL parameter
	userID := c.Params("id")

	if userID == "" {
		log.Printf("Missing user ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "User ID is required",
		})
	}

	log.Printf("Getting user info for ID: %s", userID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for user info")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Profile collection ID
	profileCollection := "6852be26001922ea0c85"

	// Build the URL to search Profile collection by UserID attribute
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	log.Printf("Searching user profile by UserID: %s", userID)
	log.Printf("Profile collection URL: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite profile request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite profile request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch user profile",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite profile API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch user profiles",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var profilesResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &profilesResponse); err != nil {
		log.Printf("Failed to parse profiles response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse profiles response",
		})
	}

	// Check if documents were found
	documents, exists := profilesResponse["documents"]
	if !exists {
		return c.Status(500).JSON(fiber.Map{
			"error": "No documents field in response",
		})
	}

	documentsArray, ok := documents.([]interface{})
	if !ok || len(documentsArray) == 0 {
		log.Printf("No profiles found in collection")
		return c.Status(404).JSON(fiber.Map{
			"error":   "User profile not found",
			"user_id": userID,
		})
	}

	log.Printf("Found %d profiles in collection, searching for UserID: %s", len(documentsArray), userID)

	// Search through all profiles for matching UserID
	for i, doc := range documentsArray {
		docMap, ok := doc.(map[string]interface{})
		if !ok {
			log.Printf("Profile %d has invalid format", i)
			continue
		}

		// Check if this profile has the matching UserID
		if userIDField, exists := docMap["UserID"]; exists {
			if userIDStr, ok := userIDField.(string); ok {
				log.Printf("Profile %d has UserID: %s", i, userIDStr)
				if userIDStr == userID {
					// Found matching profile
					log.Printf("Found matching profile for UserID: %s", userID)
					return c.JSON(fiber.Map{
						"message": "User profile fetched successfully",
						"user_id": userID,
						"profile": docMap,
					})
				}
			}
		}
	}

	// No matching profile found
	log.Printf("No profile found with UserID: %s", userID)
	return c.Status(404).JSON(fiber.Map{
		"error":   "User profile not found",
		"user_id": userID,
	})
}

func Reports(c *fiber.Ctx) error {
	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for reports")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Reports collection ID
	reportsCollection := "68540de400285bfcb5b6"

	// Build the URL to get all documents from Reports collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, reportsCollection)

	log.Printf("Fetching reports from collection: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite reports request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite reports request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch reports",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite reports API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch reports from Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var reportsResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &reportsResponse); err != nil {
		log.Printf("Failed to parse reports response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse reports response",
		})
	}

	// Log success
	if documents, exists := reportsResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			log.Printf("Successfully fetched %d reports from Appwrite", len(documentsArray))
		}
	}

	// Return the reports data
	return c.JSON(fiber.Map{
		"message": "Reports fetched successfully",
		"data":    reportsResponse,
	})
}

func DeleteUser(c *fiber.Ctx) error {
	// Get the user ID from the URL parameter
	userID := c.Params("id")

	if userID == "" {
		log.Printf("Missing user ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "User ID is required",
		})
	}

	log.Printf("Deleting user with ID: %s", userID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" {
		log.Printf("Appwrite configuration missing for user deletion")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Build the Appwrite Users API URL
	url := fmt.Sprintf("%s/users/%s", appwriteEndpoint, userID)

	log.Printf("Deleting user from Appwrite: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite delete user request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite delete user request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to delete user",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite user deletion error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to delete user in Appwrite",
			"details": string(bodyBytes),
		})
	}

	log.Printf("User deleted successfully from Appwrite with ID: %s", userID)

	// Return success response
	return c.JSON(fiber.Map{
		"message": "User deleted successfully",
		"user_id": userID,
	})

}

func GetProcedures(c *fiber.Ctx) error {

	// Get Appwrite configuration
	// 68909fb1002f5cf36505 Procedure
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for procedures")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Procedure collection ID
	procedureCollection := "68909fb1002f5cf36505"

	// Build the URL to get all documents from Procedure collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, procedureCollection)

	log.Printf("Fetching procedures from collection: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite procedures request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite procedures request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch procedures",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite procedures API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch procedures from Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var proceduresResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &proceduresResponse); err != nil {
		log.Printf("Failed to parse procedures response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse procedures response",
		})
	}

	// Log success
	if documents, exists := proceduresResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			log.Printf("Successfully fetched %d procedures from Appwrite", len(documentsArray))
		}
	}

	return c.JSON(fiber.Map{
		"message": "Procedures fetched successfully",
		"data":    proceduresResponse,
	})
}

func GetDocumentsUploadList(c *fiber.Ctx) error {
	// Get the ID from the URL parameter
	patientUserID := c.Params("id")

	if patientUserID == "" {
		log.Printf("Missing patient user ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient User ID is required",
		})
	}

	log.Printf("Getting documents upload list for patient user ID: %s", patientUserID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for documents upload")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// DocumentsUpload collection ID
	documentsUploadCollection := "688b5c0c00381f295042"

	// Build the URL to get documents from DocumentsUpload collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, documentsUploadCollection)

	log.Printf("Fetching documents upload from collection: %s", url)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite documents upload request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite documents upload request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch documents upload",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite documents upload API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch documents upload from Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var documentsResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &documentsResponse); err != nil {
		log.Printf("Failed to parse documents upload response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse documents upload response",
		})
	}

	// Filter documents by PatientUserID
	var filteredDocuments []interface{}
	if documents, exists := documentsResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			log.Printf("Found %d documents in collection, filtering by PatientUserID: %s", len(documentsArray), patientUserID)

			for _, doc := range documentsArray {
				if docMap, ok := doc.(map[string]interface{}); ok {
					if patientUserIDField, exists := docMap["PatientUserID"]; exists {
						if patientUserIDStr, ok := patientUserIDField.(string); ok && patientUserIDStr == patientUserID {
							filteredDocuments = append(filteredDocuments, docMap)
						}
					}
				}
			}
		}
	}

	log.Printf("Successfully fetched %d documents upload for PatientUserID: %s", len(filteredDocuments), patientUserID)

	return c.JSON(fiber.Map{
		"message":         "Documents upload fetched successfully",
		"patient_user_id": patientUserID,
		"documents":       filteredDocuments,
		"count":           len(filteredDocuments),
	})
}

func UploadDocument(c *fiber.Ctx) error {
	log.Printf("=== DOCUMENT UPLOAD REQUEST ===")

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for document upload")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Get form data
	userID := c.FormValue("UserID")
	patientUserID := c.FormValue("PatientUserID")
	procedureType := c.FormValue("ProcedureType")

	if userID == "" || patientUserID == "" || procedureType == "" {
		log.Printf("Missing required fields - UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)
		return c.Status(400).JSON(fiber.Map{
			"error": "UserID, PatientUserID, and ProcedureType are required",
		})
	}

	// Get the uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		log.Printf("Failed to get uploaded file: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "File is required",
		})
	}

	log.Printf("Uploading file: %s, Size: %d bytes", file.Filename, file.Size)
	log.Printf("UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)

	// Step 1: Upload file to Appwrite Storage
	documentUploadBucket := "688b593f000fe254dd5a"
	storageURL := fmt.Sprintf("%s/storage/buckets/%s/files", appwriteEndpoint, documentUploadBucket)

	// Open the uploaded file
	fileContent, err := file.Open()
	if err != nil {
		log.Printf("Failed to open uploaded file: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process uploaded file",
		})
	}
	defer fileContent.Close()

	// Create multipart form for Appwrite storage upload
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// Add fileId field (let Appwrite generate unique ID)
	writer.WriteField("fileId", "unique()")

	// Add file field
	part, err := writer.CreateFormFile("file", file.Filename)
	if err != nil {
		log.Printf("Failed to create form file: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare file upload",
		})
	}

	// Copy file content to form
	_, err = io.Copy(part, fileContent)
	if err != nil {
		log.Printf("Failed to copy file content: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare file upload",
		})
	}

	writer.Close()

	// Create HTTP request for storage upload
	req, err := http.NewRequest("POST", storageURL, &requestBody)
	if err != nil {
		log.Printf("Failed to create storage upload request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create upload request",
		})
	}

	// Set headers for Appwrite Storage API
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the storage upload request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to upload file to storage: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to upload file to storage",
		})
	}
	defer resp.Body.Close()

	// Read storage response
	storageBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read storage response body: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read storage response",
		})
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Printf("Storage upload failed - Status: %d, Response: %s", resp.StatusCode, string(storageBodyBytes))
		log.Printf("File upload failed for: %s (Size: %d bytes)", file.Filename, file.Size)
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to upload file to storage",
			"details": string(storageBodyBytes),
			"file":    file.Filename,
		})
	}

	// Parse storage response to get file ID
	var storageResponse map[string]interface{}
	if err := json.Unmarshal(storageBodyBytes, &storageResponse); err != nil {
		log.Printf("Failed to parse storage response: %v", err)
		log.Printf("Raw storage response: %s", string(storageBodyBytes))
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to parse storage response",
			"details": "Invalid JSON response from storage service",
		})
	}

	fileID, exists := storageResponse["$id"].(string)
	if !exists {
		log.Printf("File ID not found in storage response: %s", string(storageBodyBytes))
		return c.Status(500).JSON(fiber.Map{
			"error":   "File ID not returned from storage",
			"details": "Storage response missing file ID",
		})
	}

	log.Printf("File uploaded to storage successfully with ID: %s", fileID)
	log.Printf("Storage response: %s", string(storageBodyBytes))

	// Step 2: Create database record in DocumentsUpload collection
	documentsUploadCollection := "688b5c0c00381f295042"
	databaseURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, documentsUploadCollection)

	// Create the document payload
	documentData := map[string]interface{}{
		"documentId": "unique()", // Let Appwrite generate unique ID
		"data": map[string]interface{}{
			"UserID":        userID,
			"PatientUserID": patientUserID,
			"ProcedureType": procedureType,
			"Documents":     fileID, // Link to the uploaded file
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(documentData)
	if err != nil {
		log.Printf("Failed to marshal document data: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare database record",
		})
	}

	log.Printf("Creating database record: %s", string(jsonData))

	// Create HTTP request for database
	dbReq, err := http.NewRequest("POST", databaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Failed to create database request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create database request",
		})
	}

	// Set headers for Appwrite Database API
	dbReq.Header.Set("Content-Type", "application/json")
	dbReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
	dbReq.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the database request
	dbResp, err := client.Do(dbReq)
	if err != nil {
		log.Printf("Network error while creating database record: %v", err)

		// ROLLBACK: Delete the uploaded file from storage since database request failed
		log.Printf("Rolling back: Deleting uploaded file %s from storage due to network error", fileID)

		deleteStorageURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s", appwriteEndpoint, documentUploadBucket, fileID)
		deleteReq, deleteErr := http.NewRequest("DELETE", deleteStorageURL, nil)
		if deleteErr != nil {
			log.Printf("Failed to create rollback delete request: %v", deleteErr)
		} else {
			deleteReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
			deleteReq.Header.Set("X-Appwrite-Key", appwriteKey)

			deleteResp, deleteErr := client.Do(deleteReq)
			if deleteErr != nil {
				log.Printf("Failed to execute rollback delete: %v", deleteErr)
			} else {
				deleteResp.Body.Close()
				if deleteResp.StatusCode == http.StatusNoContent || deleteResp.StatusCode == http.StatusOK {
					log.Printf("Successfully rolled back: File %s deleted from storage", fileID)
				} else {
					log.Printf("Rollback delete failed with status: %d - File %s may be orphaned in storage", deleteResp.StatusCode, fileID)
				}
			}
		}

		return c.Status(500).JSON(fiber.Map{
			"error":   "Network error while creating database record - file upload rolled back",
			"details": err.Error(),
		})
	}
	defer dbResp.Body.Close()

	// Read database response
	dbBodyBytes, err := io.ReadAll(dbResp.Body)
	if err != nil {
		log.Printf("Failed to read database response body: %v", err)

		// Note: We don't rollback here since the database operation might have succeeded
		// but we just can't read the response
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to read database response",
			"details": "Database operation status unknown",
		})
	}

	if dbResp.StatusCode != http.StatusCreated && dbResp.StatusCode != http.StatusOK {
		log.Printf("Database record creation failed - Status: %d, Response: %s", dbResp.StatusCode, string(dbBodyBytes))

		// ROLLBACK: Delete the uploaded file from storage since database record creation failed
		log.Printf("Rolling back: Deleting uploaded file %s from storage due to database failure", fileID)

		deleteStorageURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s", appwriteEndpoint, documentUploadBucket, fileID)
		deleteReq, err := http.NewRequest("DELETE", deleteStorageURL, nil)
		if err != nil {
			log.Printf("Failed to create rollback delete request: %v", err)
		} else {
			deleteReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
			deleteReq.Header.Set("X-Appwrite-Key", appwriteKey)

			deleteResp, err := client.Do(deleteReq)
			if err != nil {
				log.Printf("Failed to execute rollback delete: %v", err)
			} else {
				deleteResp.Body.Close()
				if deleteResp.StatusCode == http.StatusNoContent || deleteResp.StatusCode == http.StatusOK {
					log.Printf("Successfully rolled back: File %s deleted from storage", fileID)
				} else {
					log.Printf("Rollback delete failed with status: %d - File %s may be orphaned in storage", deleteResp.StatusCode, fileID)
				}
			}
		}

		return c.Status(dbResp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to create database record - file upload rolled back",
			"details": string(dbBodyBytes),
		})
	}

	// Parse database response to get document ID for logging
	var dbResponse map[string]interface{}
	if err := json.Unmarshal(dbBodyBytes, &dbResponse); err != nil {
		log.Printf("Warning: Failed to parse database response for logging: %v", err)
	} else {
		if docID, exists := dbResponse["$id"].(string); exists {
			log.Printf("Database record created successfully with ID: %s", docID)
		}
	}

	log.Printf("=== DOCUMENT UPLOAD COMPLETED SUCCESSFULLY ===")
	log.Printf("File ID: %s, Filename: %s, Size: %d bytes", fileID, file.Filename, file.Size)
	log.Printf("UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)

	// Step 3: Look up patient info (name, email) from Appwrite using patientUserID
	log.Printf("Looking up patient info for PatientUserID: %s", patientUserID)

	// Use reusable function to get patient info
	patientName, patientEmail, err := getPatientInfo(patientUserID)
	if err != nil {
		log.Printf("Failed to get patient info: %v", err)
		// Continue without patient info - don't fail the upload
		patientName = ""
		patientEmail = ""
	} else {
		log.Printf("Patient lookup successful - Name: %s, Email: %s", patientName, patientEmail)
	}

	// Step 4: Create Documenso document if we have patient info
	if patientName != "" && patientEmail != "" {
		log.Printf("Creating Documenso document for patient: %s (%s)", patientName, patientEmail)

		documensoKey := os.Getenv("DOCUMENSO_KEY")
		if documensoKey == "" {
			log.Printf("DOCUMENSO_KEY environment variable not set")
		} else {
			// Create Documenso document payload
			documensoPayload := map[string]interface{}{
				"title":      "Surgassists Document",
				"externalId": patientUserID,
				"recipients": []map[string]interface{}{
					{
						"name":         patientName,
						"email":        patientEmail,
						"role":         "SIGNER",
						"signingOrder": nil,
					},
				},
				"meta": map[string]interface{}{},
				"authOptions": map[string]interface{}{
					"globalAccessAuth": []interface{}{},
					"globalActionAuth": []interface{}{},
				},
				"formValues": map[string]interface{}{
					"Surname": "", // Test with Surname field using patient name
				},
			}

			// Convert to JSON
			documensoJSON, err := json.Marshal(documensoPayload)
			if err != nil {
				log.Printf("Failed to marshal Documenso payload: %v", err)
			} else {
				log.Printf("Documenso payload: %s", string(documensoJSON))

				// Create HTTP request to Documenso
				documensoURL := "https://app.documenso.com/api/v1/documents"
				docReq, err := http.NewRequest("POST", documensoURL, bytes.NewBuffer(documensoJSON))
				if err != nil {
					log.Printf("Failed to create Documenso request: %v", err)
				} else {
					// Set headers
					docReq.Header.Set("Content-Type", "application/json")
					docReq.Header.Set("Authorization", documensoKey)

					// Send the request
					docResp, err := client.Do(docReq)
					if err != nil {
						log.Printf("Failed to send Documenso request: %v", err)
					} else {
						defer docResp.Body.Close()

						// Read response
						docBodyBytes, err := io.ReadAll(docResp.Body)
						if err != nil {
							log.Printf("Failed to read Documenso response: %v", err)
						} else {
							log.Printf("Documenso response status: %d", docResp.StatusCode)
							log.Printf("Documenso response: %s", string(docBodyBytes))

							if docResp.StatusCode == http.StatusCreated || docResp.StatusCode == http.StatusOK {
								// Parse the Documenso response
								var documensoResponse map[string]interface{}
								if err := json.Unmarshal(docBodyBytes, &documensoResponse); err != nil {
									log.Printf("Failed to parse Documenso response: %v", err)
								} else {
									// Extract key information from response and store for next steps
									var documensoDocumentId interface{}
									var recipientId interface{}
									var uploadUrl string
									var signingUrl string
									var recipientToken string

									if documentId, exists := documensoResponse["documentId"]; exists {
										documensoDocumentId = documentId
										log.Printf("Documenso Document ID: %v", documentId)
									}

									if uploadUrlValue, exists := documensoResponse["uploadUrl"]; exists {
										if uploadUrlStr, ok := uploadUrlValue.(string); ok {
											uploadUrl = uploadUrlStr
											log.Printf("Documenso Upload URL: %s", uploadUrl)
										}
									}

									if recipients, exists := documensoResponse["recipients"].([]interface{}); exists && len(recipients) > 0 {
										if recipient, ok := recipients[0].(map[string]interface{}); ok {
											if recipientIdValue, exists := recipient["recipientId"]; exists {
												recipientId = recipientIdValue
												log.Printf("Recipient ID: %v", recipientId)
											}
											if signingUrlValue, exists := recipient["signingUrl"]; exists {
												if signingUrlStr, ok := signingUrlValue.(string); ok {
													signingUrl = signingUrlStr
													log.Printf("Signing URL: %s", signingUrl)
												}
											}
											if tokenValue, exists := recipient["token"]; exists {
												if tokenStr, ok := tokenValue.(string); ok {
													recipientToken = tokenStr
													log.Printf("Recipient Token: %s", recipientToken)
												}
											}
										}
									}

									// Step 5: Upload file to Documenso using the uploadUrl
									if uploadUrl != "" {
										log.Printf("Step 5: Uploading file to Documenso presigned URL")

										// Reopen the uploaded file
										fileContent, err := file.Open()
										if err != nil {
											log.Printf("Failed to reopen file for Documenso upload: %v", err)
										} else {
											defer fileContent.Close()

											// Create multipart form for Documenso upload
											var documensoRequestBody bytes.Buffer
											documensoWriter := multipart.NewWriter(&documensoRequestBody)

											// Add file field (empty key as shown in curl)
											part, err := documensoWriter.CreateFormFile("", file.Filename)
											if err != nil {
												log.Printf("Failed to create Documenso form file: %v", err)
											} else {
												// Copy file content to form
												_, err = io.Copy(part, fileContent)
												if err != nil {
													log.Printf("Failed to copy file content for Documenso: %v", err)
												} else {
													documensoWriter.Close()

													// Create PUT request to Documenso uploadUrl
													documensoUploadReq, err := http.NewRequest("PUT", uploadUrl, &documensoRequestBody)
													if err != nil {
														log.Printf("Failed to create Documenso upload request: %v", err)
													} else {
														// Set content type for multipart form
														documensoUploadReq.Header.Set("Content-Type", documensoWriter.FormDataContentType())

														// Send the upload request
														documensoUploadResp, err := client.Do(documensoUploadReq)
														if err != nil {
															log.Printf("Failed to upload file to Documenso: %v", err)
														} else {
															defer documensoUploadResp.Body.Close()

															// Read upload response
															uploadRespBytes, err := io.ReadAll(documensoUploadResp.Body)
															if err != nil {
																log.Printf("Failed to read Documenso upload response: %v", err)
															} else {
																log.Printf("Documenso upload response status: %d", documensoUploadResp.StatusCode)
																log.Printf("Documenso upload response: %s", string(uploadRespBytes))

																if documensoUploadResp.StatusCode == http.StatusOK || documensoUploadResp.StatusCode == http.StatusNoContent {
																	log.Printf("File uploaded to Documenso successfully!")
																} else {
																	log.Printf("Documenso file upload failed with status: %d", documensoUploadResp.StatusCode)
																}
															}
														}
													}
												}
											}
										}
									} else {
										log.Printf("No uploadUrl received from Documenso, skipping file upload")
									}

									// Step 6: Use the captured IDs for next operations
									log.Printf("=== CAPTURED DOCUMENSO DATA ===")
									log.Printf("Document ID: %v", documensoDocumentId)
									log.Printf("Recipient ID: %v", recipientId)
									log.Printf("Signing URL: %s", signingUrl)
									log.Printf("Recipient Token: %s", recipientToken)
									log.Printf("=== READY FOR NEXT STEP ===")

									// Step 7: Add signature field to Documenso document
									if documensoDocumentId != nil && recipientId != nil {
										log.Printf("Step 7: Adding signature field to Documenso document")

										// Create signature field payload for A4 page 3
										signatureFieldPayload := []map[string]interface{}{
											{
												"recipientId": recipientId,
												"type":        "SIGNATURE",
												"pageNumber":  3,
												"pageX":       40,    // X position from left (in points)
												"pageY":       91.51, // Y position from top (in points) - near bottom of A4
												"pageWidth":   12,    // Width of signature field (in points)
												"pageHeight":  5,     // Height of signature field (in points)
											},
											{
												"recipientId": recipientId,
												"type":        "CHECKBOX",
												"pageNumber":  3,
												"pageX":       4.64,
												"pageY":       3.48,
												"pageWidth":   10,
												"pageHeight":  10,
												"fieldMeta": map[string]interface{}{
													"required": true,
													"type":     "checkbox",
													"value":    "false", // or "true"
												},
											},
										}

										// Convert to JSON
										signatureJSON, err := json.Marshal(signatureFieldPayload)
										if err != nil {
											log.Printf("Failed to marshal signature field payload: %v", err)
										} else {
											log.Printf("Signature field payload: %s", string(signatureJSON))

											// Create HTTP request to add signature field
											signatureFieldURL := fmt.Sprintf("https://app.documenso.com/api/v1/documents/%v/fields", documensoDocumentId)
											signatureReq, err := http.NewRequest("POST", signatureFieldURL, bytes.NewBuffer(signatureJSON))
											if err != nil {
												log.Printf("Failed to create signature field request: %v", err)
											} else {
												// Set headers
												signatureReq.Header.Set("Content-Type", "application/json")
												signatureReq.Header.Set("Authorization", documensoKey)

												// Send the request
												signatureResp, err := client.Do(signatureReq)
												if err != nil {
													log.Printf("Failed to send signature field request: %v", err)
												} else {
													defer signatureResp.Body.Close()

													// Read response
													signatureRespBytes, err := io.ReadAll(signatureResp.Body)
													if err != nil {
														log.Printf("Failed to read signature field response: %v", err)
													} else {
														log.Printf("Signature field response status: %d", signatureResp.StatusCode)
														log.Printf("Signature field response: %s", string(signatureRespBytes))

														if signatureResp.StatusCode == http.StatusCreated || signatureResp.StatusCode == http.StatusOK {
															log.Printf("Signature field added successfully to Documenso document!")
														} else {
															log.Printf("Failed to add signature field with status: %d", signatureResp.StatusCode)
														}
													}
												}
											}
										}
									} else {
										log.Printf("Missing documentId or recipientId, skipping signature field creation")
									}
								}

								log.Printf("Documenso document created successfully")
							} else {
								log.Printf("Documenso document creation failed with status: %d", docResp.StatusCode)
							}
						}
					}
				}
			}
		}
	} else {
		log.Printf("Skipping Documenso document creation - missing patient info")
	}

	log.Printf("=== END DOCUMENT UPLOAD ===")

	log.Printf("=== END DOCUMENT UPLOAD ===")

	return c.JSON(fiber.Map{
		"message":         "Document uploaded successfully",
		"file_id":         fileID,
		"user_id":         userID,
		"patient_user_id": patientUserID,
		"procedure_type":  procedureType,
		"filename":        file.Filename,
		"size":            file.Size,
	})
}

func DeleteDoc(c *fiber.Ctx) error {
	// Get the document ID from the URL parameter
	documentID := c.Params("id")

	if documentID == "" {
		log.Printf("Missing document ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Document ID is required",
		})
	}

	log.Printf("Deleting document with ID: %s", documentID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for document deletion")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Step 1: Delete from Storage (DocumentUpload bucket)
	storageURL := fmt.Sprintf("%s/storage/buckets/688b593f000fe254dd5a/files/%s", appwriteEndpoint, documentID)

	req, err := http.NewRequest("DELETE", storageURL, nil)
	if err != nil {
		log.Printf("Failed to create storage delete request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create storage delete request",
		})
	}

	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to delete from storage: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to delete from storage",
		})
	}
	resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		log.Printf("Storage deletion failed with status: %d", resp.StatusCode)
	} else {
		log.Printf("Successfully deleted from storage: %s", documentID)
	}

	// Step 2: Find and delete from DocumentsUpload collection where Documents = documentID
	documentsUploadCollection := "688b5c0c00381f295042"

	// First, get all documents to find the one with matching Documents field
	listURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, documentsUploadCollection)

	req, err = http.NewRequest("GET", listURL, nil)
	if err != nil {
		log.Printf("Failed to create list request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create list request",
		})
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	resp, err = client.Do(req)
	if err != nil {
		log.Printf("Failed to list documents: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to list documents",
		})
	}
	defer resp.Body.Close()

	bodyBytes, _ := io.ReadAll(resp.Body)
	var documentsResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &documentsResponse); err != nil {
		log.Printf("Failed to parse documents response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse documents response",
		})
	}

	// Find document with matching Documents field
	var targetDocumentID string
	if documents, exists := documentsResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			for _, doc := range documentsArray {
				if docMap, ok := doc.(map[string]interface{}); ok {
					if documentsField, exists := docMap["Documents"]; exists {
						if documentsStr, ok := documentsField.(string); ok && documentsStr == documentID {
							if docID, exists := docMap["$id"]; exists {
								if docIDStr, ok := docID.(string); ok {
									targetDocumentID = docIDStr
									break
								}
							}
						}
					}
				}
			}
		}
	}

	if targetDocumentID == "" {
		log.Printf("No document found in collection with Documents field = %s", documentID)
		return c.Status(404).JSON(fiber.Map{
			"error": "Document not found in collection",
		})
	}

	// Delete the found document from collection
	deleteURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, documentsUploadCollection, targetDocumentID)

	req, err = http.NewRequest("DELETE", deleteURL, nil)
	if err != nil {
		log.Printf("Failed to create collection delete request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create collection delete request",
		})
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	resp, err = client.Do(req)
	if err != nil {
		log.Printf("Failed to delete from collection: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to delete from collection",
		})
	}
	resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		log.Printf("Collection deletion failed with status: %d", resp.StatusCode)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to delete from collection",
		})
	}

	log.Printf("Successfully deleted document %s from both storage and collection", documentID)

	return c.JSON(fiber.Map{
		"message":     "Document deleted successfully",
		"document_id": documentID,
	})
}

// getPatientInfo retrieves patient name and email from Patients collection by document ID
func getPatientInfo(patientUserID string) (name string, email string, err error) {
	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		return "", "", fmt.Errorf("appwrite configuration missing")
	}

	// Patients collection ID
	patientsCollection := "6887435d000eae4f6123"

	// Build the URL to get specific patient document by ID
	patientsURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, patientsCollection, patientUserID)

	// Create HTTP request to get specific patient document
	req, err := http.NewRequest("GET", patientsURL, nil)
	if err != nil {
		return "", "", fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers for Appwrite Database API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("failed to fetch patient data: %v", err)
	}
	defer resp.Body.Close()

	// Read response
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("failed to read response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", "", fmt.Errorf("patient lookup failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse the response - this is a single document, not an array
	var patientDoc map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &patientDoc); err != nil {
		return "", "", fmt.Errorf("failed to parse response: %v", err)
	}

	// Extract Name and Email from the patient document
	if nameValue, exists := patientDoc["Name"]; exists {
		if nameStr, ok := nameValue.(string); ok {
			name = nameStr
		}
	}

	if emailValue, exists := patientDoc["Email"]; exists {
		if emailStr, ok := emailValue.(string); ok {
			email = emailStr
		}
	}

	return name, email, nil
}

func UploadDocumentProcessing(c *fiber.Ctx) error {
	log.Printf("=== DOCUMENT PROCESSING UPLOAD REQUEST ===")

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for document processing upload")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Get form data
	userID := c.FormValue("UserID")
	patientUserID := c.FormValue("PatientUserID")
	procedureType := c.FormValue("ProcedureType")

	if userID == "" || patientUserID == "" || procedureType == "" {
		log.Printf("Missing required fields - UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)
		return c.Status(400).JSON(fiber.Map{
			"error": "UserID, PatientUserID, and ProcedureType are required",
		})
	}

	// Get the uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		log.Printf("Failed to get uploaded file: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "File is required",
		})
	}

	log.Printf("Processing file: %s, Size: %d bytes", file.Filename, file.Size)
	log.Printf("UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)

	// Step 1: Upload file to Appwrite Storage
	documentUploadBucket := "688b593f000fe254dd5a"
	storageURL := fmt.Sprintf("%s/storage/buckets/%s/files", appwriteEndpoint, documentUploadBucket)

	// Open the uploaded file
	fileContent, err := file.Open()
	if err != nil {
		log.Printf("Failed to open uploaded file: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to process uploaded file",
		})
	}
	defer fileContent.Close()

	// Create multipart form for Appwrite storage upload
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// Add fileId field (let Appwrite generate unique ID)
	writer.WriteField("fileId", "unique()")

	// Add file field
	part, err := writer.CreateFormFile("file", file.Filename)
	if err != nil {
		log.Printf("Failed to create form file: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare file upload",
		})
	}

	// Copy file content to form
	_, err = io.Copy(part, fileContent)
	if err != nil {
		log.Printf("Failed to copy file content: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare file upload",
		})
	}

	writer.Close()

	// Create HTTP request for storage upload
	req, err := http.NewRequest("POST", storageURL, &requestBody)
	if err != nil {
		log.Printf("Failed to create storage upload request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create upload request",
		})
	}

	// Set headers for Appwrite Storage API
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the storage upload request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to upload file to storage: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to upload file to storage",
		})
	}
	defer resp.Body.Close()

	// Read storage response
	storageBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read storage response body: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read storage response",
		})
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Printf("Storage upload failed - Status: %d, Response: %s", resp.StatusCode, string(storageBodyBytes))
		log.Printf("File upload failed for: %s (Size: %d bytes)", file.Filename, file.Size)
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to upload file to storage",
			"details": string(storageBodyBytes),
			"file":    file.Filename,
		})
	}

	// Parse storage response to get file ID
	var storageResponse map[string]interface{}
	if err := json.Unmarshal(storageBodyBytes, &storageResponse); err != nil {
		log.Printf("Failed to parse storage response: %v", err)
		log.Printf("Raw storage response: %s", string(storageBodyBytes))
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to parse storage response",
			"details": "Invalid JSON response from storage service",
		})
	}

	fileID, exists := storageResponse["$id"].(string)
	if !exists {
		log.Printf("File ID not found in storage response: %s", string(storageBodyBytes))
		return c.Status(500).JSON(fiber.Map{
			"error":   "File ID not returned from storage",
			"details": "Storage response missing file ID",
		})
	}

	log.Printf("File uploaded to storage successfully with ID: %s", fileID)
	log.Printf("Storage response: %s", string(storageBodyBytes))

	// Step 2: Create database record in DocumentsUpload collection
	documentsUploadCollection := "688b5c0c00381f295042"
	databaseURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, documentsUploadCollection)

	// Create the document payload
	documentData := map[string]interface{}{
		"documentId": "unique()", // Let Appwrite generate unique ID
		"data": map[string]interface{}{
			"UserID":        userID,
			"PatientUserID": patientUserID,
			"ProcedureType": procedureType,
			"Documents":     fileID, // Link to the uploaded file
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(documentData)
	if err != nil {
		log.Printf("Failed to marshal document data: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare database record",
		})
	}

	log.Printf("Creating database record: %s", string(jsonData))

	// Create HTTP request for database
	dbReq, err := http.NewRequest("POST", databaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Failed to create database request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create database request",
		})
	}

	// Set headers for Appwrite Database API
	dbReq.Header.Set("Content-Type", "application/json")
	dbReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
	dbReq.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the database request
	dbResp, err := client.Do(dbReq)
	if err != nil {
		log.Printf("Network error while creating database record: %v", err)

		// ROLLBACK: Delete the uploaded file from storage since database request failed
		log.Printf("Rolling back: Deleting uploaded file %s from storage due to network error", fileID)

		deleteStorageURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s", appwriteEndpoint, documentUploadBucket, fileID)
		deleteReq, deleteErr := http.NewRequest("DELETE", deleteStorageURL, nil)
		if deleteErr != nil {
			log.Printf("Failed to create rollback delete request: %v", deleteErr)
		} else {
			deleteReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
			deleteReq.Header.Set("X-Appwrite-Key", appwriteKey)

			deleteResp, deleteErr := client.Do(deleteReq)
			if deleteErr != nil {
				log.Printf("Failed to execute rollback delete: %v", deleteErr)
			} else {
				deleteResp.Body.Close()
				if deleteResp.StatusCode == http.StatusNoContent || deleteResp.StatusCode == http.StatusOK {
					log.Printf("Successfully rolled back: File %s deleted from storage", fileID)
				} else {
					log.Printf("Rollback delete failed with status: %d - File %s may be orphaned in storage", deleteResp.StatusCode, fileID)
				}
			}
		}

		return c.Status(500).JSON(fiber.Map{
			"error":   "Network error while creating database record - file upload rolled back",
			"details": err.Error(),
		})
	}
	defer dbResp.Body.Close()

	// Read database response
	dbBodyBytes, err := io.ReadAll(dbResp.Body)
	if err != nil {
		log.Printf("Failed to read database response body: %v", err)

		// Note: We don't rollback here since the database operation might have succeeded
		// but we just can't read the response
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to read database response",
			"details": "Database operation status unknown",
		})
	}

	if dbResp.StatusCode != http.StatusCreated && dbResp.StatusCode != http.StatusOK {
		log.Printf("Database record creation failed - Status: %d, Response: %s", dbResp.StatusCode, string(dbBodyBytes))

		// ROLLBACK: Delete the uploaded file from storage since database record creation failed
		log.Printf("Rolling back: Deleting uploaded file %s from storage due to database failure", fileID)

		deleteStorageURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s", appwriteEndpoint, documentUploadBucket, fileID)
		deleteReq, err := http.NewRequest("DELETE", deleteStorageURL, nil)
		if err != nil {
			log.Printf("Failed to create rollback delete request: %v", err)
		} else {
			deleteReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
			deleteReq.Header.Set("X-Appwrite-Key", appwriteKey)

			deleteResp, err := client.Do(deleteReq)
			if err != nil {
				log.Printf("Failed to execute rollback delete: %v", err)
			} else {
				deleteResp.Body.Close()
				if deleteResp.StatusCode == http.StatusNoContent || deleteResp.StatusCode == http.StatusOK {
					log.Printf("Successfully rolled back: File %s deleted from storage", fileID)
				} else {
					log.Printf("Rollback delete failed with status: %d - File %s may be orphaned in storage", deleteResp.StatusCode, fileID)
				}
			}
		}

		return c.Status(dbResp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to create database record - file upload rolled back",
			"details": string(dbBodyBytes),
		})
	}

	// Parse database response to get document ID for logging
	var dbResponse map[string]interface{}
	if err := json.Unmarshal(dbBodyBytes, &dbResponse); err != nil {
		log.Printf("Warning: Failed to parse database response for logging: %v", err)
	} else {
		if docID, exists := dbResponse["$id"].(string); exists {
			log.Printf("Database record created successfully with ID: %s", docID)
		}
	}

	log.Printf("=== DOCUMENT PROCESSING UPLOAD COMPLETED SUCCESSFULLY ===")
	log.Printf("File ID: %s, Filename: %s, Size: %d bytes", fileID, file.Filename, file.Size)
	log.Printf("UserID: %s, PatientUserID: %s, ProcedureType: %s", userID, patientUserID, procedureType)

	return c.JSON(fiber.Map{
		"message":         "Document uploaded and processed successfully",
		"file_id":         fileID,
		"user_id":         userID,
		"patient_user_id": patientUserID,
		"procedure_type":  procedureType,
		"filename":        file.Filename,
		"size":            file.Size,
	})
}
