package routes

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	//"github.com/applegold/surgassists-ar-backend/utils"
	"github.com/gofiber/fiber/v2"
)

func GetPDFdocumentsForSignature(c *fiber.Ctx) error {
	log.Printf("=== GET PDF DOCUMENTS FOR SIGNATURE ===")

	// Get the ID from the URL parameter
	patientID := c.Params("id")

	if patientID == "" {
		log.Printf("Missing patient ID in URL")
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient ID is required",
		})
	}

	log.Printf("Getting PDF documents for patient ID: %s", patientID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing")
		return c.Status(500).JSON(fiber.Map{
			"error": "Appwrite configuration missing",
		})
	}

	// Consent collection ID
	consentCollection := "6853de08003879050263"

	// Build the URL to get document from Consent collection using $id
	documentURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, consentCollection, patientID)

	log.Printf("Fetching document from Consent collection: %s", documentURL)

	// Create HTTP request
	req, err := http.NewRequest("GET", documentURL, nil)
	if err != nil {
		log.Printf("Failed to create Appwrite document request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set headers for Appwrite Server API
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to send Appwrite document request: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch document",
		})
	}
	defer resp.Body.Close()

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read response body: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check response status
	if resp.StatusCode != http.StatusOK {
		log.Printf("Appwrite document API error - Status: %d, Response: %s", resp.StatusCode, string(bodyBytes))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error":   "Failed to fetch document from Appwrite",
			"details": string(bodyBytes),
		})
	}

	// Parse the response
	var documentResponse map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &documentResponse); err != nil {
		log.Printf("Failed to parse document response: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse document response",
		})
	}

	// Log the document information
	log.Printf("=== DOCUMENT RETRIEVED FROM CONSENT COLLECTION ===")
	log.Printf("Document ID: %s", patientID)
	log.Printf("Document data: %s", string(bodyBytes))

	// Log specific fields if they exist
	var documentIDSent string
	if email, exists := documentResponse["Email"]; exists {
		log.Printf("Email: %v", email)
	}
	if status, exists := documentResponse["Status"]; exists {
		log.Printf("Status: %v", status)
	}
	if userID, exists := documentResponse["UserID"]; exists {
		log.Printf("UserID: %v", userID)
	}
	if createdAt, exists := documentResponse["$createdAt"]; exists {
		log.Printf("Created At: %v", createdAt)
	}
	if docIDSent, exists := documentResponse["DocumentIDSent"]; exists {
		if docIDStr, ok := docIDSent.(string); ok {
			documentIDSent = docIDStr
			log.Printf("DocumentIDSent: %s", documentIDSent)
		}
	}
	log.Printf("=== END DOCUMENT INFO ===")

	// Fetch PDF data if DocumentIDSent exists
	var pdfData []byte
	var pdfFilename string
	if documentIDSent != "" {
		log.Printf("Fetching PDF data for DocumentIDSent: %s", documentIDSent)

		// DocumentUpload storage bucket
		documentUploadBucket := "688b593f000fe254dd5a"

		// Build the PDF download URL (not view URL)
		pdfDownloadURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s/download?project=%s",
			appwriteEndpoint, documentUploadBucket, documentIDSent, appwriteProjectID)

		log.Printf("Downloading PDF from: %s", pdfDownloadURL)

		// Create HTTP request to download PDF
		pdfReq, err := http.NewRequest("GET", pdfDownloadURL, nil)
		if err != nil {
			log.Printf("Failed to create PDF download request: %v", err)
		} else {
			// Set headers for Appwrite Storage API
			pdfReq.Header.Set("X-Appwrite-Project", appwriteProjectID)
			pdfReq.Header.Set("X-Appwrite-Key", appwriteKey)

			// Send the PDF download request
			pdfResp, err := client.Do(pdfReq)
			if err != nil {
				log.Printf("Failed to download PDF: %v", err)
			} else {
				defer pdfResp.Body.Close()

				if pdfResp.StatusCode == http.StatusOK {
					// Read PDF data
					pdfData, err = io.ReadAll(pdfResp.Body)
					if err != nil {
						log.Printf("Failed to read PDF data: %v", err)
						pdfData = nil
					} else {
						log.Printf("PDF data retrieved successfully, size: %d bytes", len(pdfData))

						// Try to get filename from Content-Disposition header
						if contentDisposition := pdfResp.Header.Get("Content-Disposition"); contentDisposition != "" {
							log.Printf("Content-Disposition: %s", contentDisposition)
							// Extract filename if present
							if strings.Contains(contentDisposition, "filename=") {
								parts := strings.Split(contentDisposition, "filename=")
								if len(parts) > 1 {
									pdfFilename = strings.Trim(parts[1], "\"")
								}
							}
						}

						if pdfFilename == "" {
							pdfFilename = fmt.Sprintf("document_%s.pdf", documentIDSent)
						}

						log.Printf("PDF filename: %s", pdfFilename)
					}
				} else {
					log.Printf("PDF download failed with status: %d", pdfResp.StatusCode)
					// Read error response
					errorBody, _ := io.ReadAll(pdfResp.Body)
					log.Printf("PDF download error response: %s", string(errorBody))
				}
			}
		}
	} else {
		log.Printf("No DocumentIDSent found, PDF data not retrieved")
	}

	/*
		// Create Documenso document from template
		var documensoResponse *utils.CreateDocumentFromTemplateResponse
		var documensoError error

		// Extract patient information for Documenso
		if email, exists := documentResponse["Email"]; exists {
			if emailStr, ok := email.(string); ok && emailStr != "" {
				log.Printf("Creating Documenso document for email: %s", emailStr)

				// Get patient name from UserID or use email as fallback
				patientName := emailStr
				if userID, exists := documentResponse["UserID"]; exists {
					if userIDStr, ok := userID.(string); ok {
						// You could fetch name from Profile collection here if needed
						// For now, using email as name
						patientName = userIDStr
					}
				}

				// Create Documenso client and document
				client := utils.NewDocumensoClient()
				documensoResponse, documensoError = client.CreateSurgassistsDocumentWithExternalId(
					patientName,
					emailStr,
					fmt.Sprintf("Consent Form - %s", patientName),
					patientID, // Use Consent document $id as external reference - unique per patient/session
				)

				if documensoError != nil {
					log.Printf("Failed to create Documenso document: %v", documensoError)
				} else {
					log.Printf("Documenso document created successfully: %d", documensoResponse.DocumentID)
				}
			}
		}
	*/

	// Declare variables for compatibility with existing code below
	// var documensoResponse *utils.CreateDocumentFromTemplateResponse = nil
	// var documensoError error = nil

	// Prepare response
	response := fiber.Map{
		"message":        "PDF documents for signature retrieved successfully",
		"patient_id":     patientID,
		"document":       documentResponse,
		"template_url":   "https://app.documenso.com/d/nr0_9M2wDXFwzi2m41WNq",
		"template_token": "nr0_9M2wDXFwzi2m41WNq",
	}

	// Include PDF data if available
	if pdfData != nil {
		// Convert PDF data to base64 for JSON response
		pdfBase64 := base64.StdEncoding.EncodeToString(pdfData)
		response["pdf_data"] = pdfBase64
		response["pdf_filename"] = pdfFilename
		response["pdf_size"] = len(pdfData)
		log.Printf("Including PDF data in response (base64 encoded)")
	}

	// Include Documenso document data if available
	// if documensoResponse != nil {
	// 	documensoData := fiber.Map{
	// 		"document_id": documensoResponse.DocumentID,
	// 		"title":       documensoResponse.Title,
	// 		"status":      documensoResponse.Status,
	// 		"externalId":  documensoResponse.ExternalId,
	// 		"signing_url": fmt.Sprintf("https://app.documenso.com/sign/%d", documensoResponse.DocumentID),
	// 	}

	// 	// Include recipient signing tokens if available
	// 	if len(documensoResponse.Recipients) > 0 {
	// 		var recipients []fiber.Map
	// 		for _, recipient := range documensoResponse.Recipients {
	// 			recipientData := fiber.Map{
	// 				"id":    recipient.ID,
	// 				"name":  recipient.Name,
	// 				"email": recipient.Email,
	// 				"role":  recipient.Role,
	// 			}

	// 			// Add signing token and URL if available
	// 			if recipient.Token != "" {
	// 				recipientData["signing_token"] = recipient.Token
	// 				recipientData["embed_url"] = fmt.Sprintf("https://app.documenso.com/sign/%s", recipient.Token)
	// 			}
	// 			if recipient.SigningUrl != "" {
	// 				recipientData["signing_url"] = recipient.SigningUrl
	// 				// Extract token from signing URL for embedding
	// 				if strings.Contains(recipient.SigningUrl, "/sign/") {
	// 					parts := strings.Split(recipient.SigningUrl, "/sign/")
	// 					if len(parts) > 1 {
	// 						recipientData["signing_token"] = parts[1]
	// 					}
	// 				}
	// 			}

	// 			recipients = append(recipients, recipientData)
	// 		}
	// 		documensoData["recipients"] = recipients
	// 	}

	// 	response["documenso"] = documensoData
	// 	log.Printf("Including Documenso document data in response")
	// } else if documensoError != nil {
	// 	response["documenso_error"] = documensoError.Error()
	// 	log.Printf("Including Documenso error in response")
	// }

	return c.JSON(response)
}
